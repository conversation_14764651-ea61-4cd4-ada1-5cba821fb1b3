// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDfX9GNdA-ahOMeJfaPYbS1bfmwd2O8FbI',
    appId: '1:300804286264:web:67e60b539e5550455403cd',
    messagingSenderId: '300804286264',
    projectId: 'toika-369',
    authDomain: 'toika-369.firebaseapp.com',
    databaseURL: 'https://toika-369-default-rtdb.firebaseio.com',
    storageBucket: 'toika-369.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDfX9GNdA-ahOMeJfaPYbS1bfmwd2O8FbI',
    appId: '1:300804286264:android:67e60b539e5550455403cd',
    messagingSenderId: '300804286264',
    projectId: 'toika-369',
    databaseURL: 'https://toika-369-default-rtdb.firebaseio.com',
    storageBucket: 'toika-369.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDfX9GNdA-ahOMeJfaPYbS1bfmwd2O8FbI',
    appId: '1:300804286264:ios:67e60b539e5550455403cd',
    messagingSenderId: '300804286264',
    projectId: 'toika-369',
    databaseURL: 'https://toika-369-default-rtdb.firebaseio.com',
    storageBucket: 'toika-369.appspot.com',
    iosBundleId: 'mu.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDfX9GNdA-ahOMeJfaPYbS1bfmwd2O8FbI',
    appId: '1:300804286264:ios:67e60b539e5550455403cd',
    messagingSenderId: '300804286264',
    projectId: 'toika-369',
    databaseURL: 'https://toika-369-default-rtdb.firebaseio.com',
    storageBucket: 'toika-369.appspot.com',
    iosBundleId: 'mu.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDfX9GNdA-ahOMeJfaPYbS1bfmwd2O8FbI',
    appId: '1:300804286264:android:67e60b539e5550455403cd',
    messagingSenderId: '300804286264',
    projectId: 'toika-369',
    databaseURL: 'https://toika-369-default-rtdb.firebaseio.com',
    storageBucket: 'toika-369.appspot.com',
  );
}
